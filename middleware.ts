import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'

// Define route matchers for different protection levels
const isProtectedRoute = createRouteMatcher(['/dashboard(.*)'])
const isOwnerRoute = createRouteMatcher(['/dashboard/owner(.*)'])
const isAdminRoute = createRouteMatcher(['/dashboard/admin(.*)'])
const isPublicDirectoryRoute = createRouteMatcher(['/directory(.*)'])

export default clerkMiddleware(async (auth, req) => {
  // Protect all dashboard routes
  if (isProtectedRoute(req)) {
    await auth.protect()

    // Get user session to check roles
    const { sessionClaims } = await auth()
    const userRole = sessionClaims?.metadata?.role as string | undefined

    // Role-based route protection
    if (isAdminRoute(req)) {
      // Admin routes require admin role
      if (userRole !== 'admin') {
        return NextResponse.redirect(new URL('/dashboard', req.url))
      }
    } else if (isOwnerRoute(req)) {
      // Owner routes require owner or admin role
      if (userRole !== 'owner' && userRole !== 'admin') {
        return NextResponse.redirect(new URL('/dashboard', req.url))
      }
    }
  }

  // Public directory routes are accessible to everyone
  // No additional protection needed for /directory routes
})

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}